// app/routes/home.account.tsx

import React, { useEffect, useState } from "react";
import { useNavigate, useLoaderData, useActionData, useLocation } from "@remix-run/react";
import {
  ActionFunction,
  LoaderFunction,
  redirect,
  json
} from "@remix-run/node";
import { destroySession, getSession } from "~/utils/session.server";
import { parseJWT } from "~/utils/token-utils";
import { DecodedToken, UserProfileDetails } from "~/types/user";
import { BackNavHeader } from "~/components/BackNavHeader";
import {
  ChevronRight,
  Languages,
  Headset,
  UserCog,
  LogOut,
  NetworkIcon
} from "lucide-react";
import Button from "~/components/Button";
import { UserInfo } from "~/components/UserInfo";
import { requireAuth } from "~/utils/clientReponse";
import { useRequireAuth } from "~/hooks/useRequireAuth";
import ErrorBoundaryComponent from "~/components/ErrorBoundary";
import { NetworkAsset } from "~/components/NetworkAssests";
import { useAppConfigStore } from "~/stores/appConfig.store";
import { AppInfo } from "~/types";
import { getUserDetails, updateUserDetails } from "~/services/user.service";
import { useLogout } from "~/hooks/useLogout";
import { formatCurrency } from "~/utils/format";
import Toast from "~/components/Toast";
import InstallPWAButton from "~/components/InstallPWAButton";
import InstallPWAiOSInstruction from "~/components/InstallPWAiOSInstruction";

interface LoaderData {
  userProfileData: UserProfileDetails;
  isBuyerOwner: boolean;
  settingUrl?: string;
  error?: string;
}

interface ActionData {
  success?: boolean;
  message?: string;
  data?: UserProfileDetails;
}

// Extend Window interface to include getAppInfo
declare global {
  interface Window {
    getAppInfo?: () => Promise<AppInfo>;
  }
}

export const loader: LoaderFunction = async ({ request }) => {
  const session = await getSession(request.headers.get("Cookie"));
  const access_token = session.get("access_token") as string | null;

  const auth = await requireAuth(request, "", false);
  if (auth && auth.authRequired) {
    return json({ ...auth, userProfileData: {}, mobileNumber: null });
  }

  if (!access_token) {
    return redirect("/login");
  }

  try {
    const decoded = parseJWT(access_token) as DecodedToken;

    // if (!decoded || !decoded.userDetails) {
    //   return redirect("/login");
    // }
    const isBuyerOwner = decoded.roles?.includes("buyer_app.WRITE");
    const response = await getUserDetails(request);
    const { mobileNumber } = decoded.userDetails;
    if (!response?.data) {
      throw new Error("Failed to get user details");
    }

    if (process.env.F_NODE_ENV === "development") {
      response.data.role = "owner";
    }

    return {
      userProfileData: response.data,
      isBuyerOwner,
      mobileNumber,
      settingUrl:
        (process.env.F_NODE_ENV === "production"
          ? "https://console.mnetonline.in/sellerSetting"
          : process.env.F_NODE_ENV === "uat"
          ? "https://comm-test.mnetlive.com/sellerSetting"
          : "http://localhost:5173/sellerSetting") +
        "?mobileNumber=" +
        mobileNumber +
        "&token=12345"
    };
  } catch (error) {
    return json({
      userProfileData: {},
      isBuyerOwner: false,
      mobileNumber: null,
      error: "Failed to load user details"
    });
  }
};

export const action: ActionFunction = async ({ request }) => {
  const session = await getSession(request.headers.get("Cookie"));
  const formData = await request.formData();

  const intent = formData.get("intent");

  if (intent === "logout") {
    return redirect("/login?redirectTo=/home", {
      headers: {
        "Set-Cookie": await destroySession(session)
      }
    });
  }

  if (intent === "update") {
    console.log(formData.get("businessName"), "ooooooooooooooooo");
    try {
      const payload = {
        firstName: formData.get("businessName") as string,
        email: formData.get("email") as string
        // mobileNumber: formData.get("mobileNumber") as string
      };

      const response = await updateUserDetails(request, payload);

      return json(
        {
          success: true,
          data: response
        },
        { status: 200 }
      );
    } catch (error) {
      console.error("Error updating user details:", error);
      return json(
        {
          success: false,
          message:
            error instanceof Error
              ? error.message
              : "Failed to update user details"
        },
        { status: 400 }
      );
    }
  }
};

export default function Account() {
  const navigate = useNavigate();
  const location = useLocation();
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [version, setVersion] = useState("");
  const { appDomain } = useAppConfigStore((state) => state);
  const { logout } = useLogout();
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");
  const [toastType, setToastType] = useState<
    "success" | "error" | "warning" | "info"
  >("error");

  useRequireAuth();

  // Use loader data and action data
  const {
    userProfileData,
    isBuyerOwner,
    error: loaderError,
    settingUrl
  } = useLoaderData<LoaderData>();
  const actionData = useActionData<ActionData>();

  // Show toast for loader errors
  useEffect(() => {
    if (loaderError) {
      setToastMessage(loaderError);
      setToastType("warning");
      setShowToast(true);
    }
  }, [loaderError]);

  // Show toast for action errors or success
  useEffect(() => {
    if (actionData) {
      if (actionData.success === false) {
        setToastMessage(actionData.message || "An error occurred");
        setToastType("error");
        setShowToast(true);
      } else if (actionData.success === true) {
        setToastMessage("Profile updated successfully");
        setToastType("success");
        setShowToast(true);
      }
    }
  }, [actionData]);

  const handleLogout = () => {
    // Clear local storage
    localStorage.clear();
    logout();
    // Submit the form to trigger the action
    // submit({ intent: "logout" }, { method: "post", action: "/home/<USER>" });
  };

  const handleBack = () => {
    // Use returnTo from state first (for dynamic returns), then from URL params
    const stateReturnTo = location.state?.returnTo;
    const effectiveReturnTo = stateReturnTo || "/home";

    // Navigate to the return path
    navigate(effectiveReturnTo);
  };

  useEffect(() => {
    async function checkAppVersion() {
      try {
        if (typeof window !== "undefined" && window.getAppInfo) {
          const appInfo = await window.getAppInfo();
          console.log("getAppInfo", appInfo);
          setVersion(appInfo?.versionName || "");
        }
      } catch (error) {
        console.error("Error fetching app info:", error);
      }
    }

    checkAppVersion();
  }, []);

  const Divider = () => {
    return <div className="w-[80%] h-[1px] bg-neutral-300 mx-auto" />;
  };

  return (
    <div className="min-h-screen w-full bg-neutral-100 no-scrollbar">
      <div className="w-full flex-col gap-0">
        <InstallPWAButton
          themeColor="Primary"
          title="Get The App"
          subtitle="For Better Experience"
        />
        <div className="sticky top-0 z-10 w-full ">
          <BackNavHeader
            buttonText="Settings"
            handleBack={handleBack}
            rightButton={
              <Button
                onClick={() => navigate("/help")}
                className="h-8 flex text-md items-center px-3  text-primary border border-primary rounded-3xl"
              >
                <Headset size={20} className="mr-1" />
                <span className="text-sm">Help</span>
              </Button>
            }
          />
        </div>
        <div className="flex flex-col items-center bg-neutral-100 p-3 gap-4 ">
          {/* User Info */}
          <UserInfo
            name={userProfileData.userName}
            email={userProfileData.email}
            phone={userProfileData.mobileNumber}
          />

          <SectionWrapper title="History">
            <SettingCardLayout
              title="Order History"
              description="View your order history"
              icon={<img src="/orders-icon.svg" alt="orders-icon" />}
              onClick={() =>
                navigate(
                  appDomain === "RET11" ? "/home/<USER>/orders" : "/home/<USER>"
                )
              }
            />

            <Divider />
            <SettingCardLayout
              title="Transactions"
              description={`Current Balance: ${
                formatCurrency(userProfileData.walletBalance || 0) || 0
              }`}
              icon={
                <img src="/transactions-icon.svg" alt="transactions-icon" />
              }
              onClick={() => navigate("/home/<USER>")}
            />
          </SectionWrapper>

          <SectionWrapper title="Settings and Preferences">
            {appDomain === "RET11" && userProfileData.role === "owner" && (
              <>
                <SettingCardLayout
                  title="Settings"
                  description="You can update your preferred language anytime"
                  icon={<Languages size={24} />}
                  onClick={() => {
                    // open new tab with url https://ret11.com/settings
                    window.open(settingUrl, "_blank");
                  }}
                />
                <Divider />
              </>
            )}
            {/* User management Section */}
            {isBuyerOwner && appDomain !== "RET11" && (
              <>
                <SettingCardLayout
                  title="User Management"
                  description="Add or active/deactivate users from here"
                  icon={<UserCog size={24} />}
                  onClick={() => navigate("/usermanagement")}
                />
                <Divider />
              </>
            )}

            {appDomain !== "RET11" && (
              <>
                <SettingCardLayout
                  title="Change Language"
                  description="You can update your preferred language anytime"
                  icon={<Languages size={24} />}
                  onClick={() => navigate("/language")}
                />
                <Divider />
              </>
            )}

            {/* Address Section */}
            <SettingCardLayout
              title="Address Book"
              description="Edit & Add New Addresses"
              icon={
                <img
                  src="/address-icon.svg"
                  alt="address-book-icon"
                  className="w-6 h-6"
                />
              }
              onClick={() =>
                navigate(
                  "/select-address?flowType=address-list&returnTo=/home/<USER>",
                  {
                    state: {
                      from: "/home/<USER>",
                      returnTo: "/home/<USER>",
                      flowType: "address-list"
                    }
                  }
                )
              }
            />
          </SectionWrapper>

          <SectionWrapper>
            <Button
              onClick={() => setShowConfirmDialog(true)}
              className="flex flex-row items-center gap-2 "
            >
              <LogOut size={24} strokeWidth={1} className="text-secondary" />
              <p className=" font-medium text-secondary">Logout</p>
            </Button>
          </SectionWrapper>
          {/* Logout Confirmation Dialog */}
          {showConfirmDialog && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-8 z-10">
              <div className="bg-white p-6 rounded-lg shadow-xl flex flex-col items-center">
                <h2 className="text-lg font-medium mb-4">Confirm Logout</h2>
                <p className="mb-6 text-md">Are you sure you want to logout?</p>
                <div className="flex justify-end space-x-4">
                  <button
                    className="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300"
                    onClick={() => setShowConfirmDialog(false)}
                  >
                    Cancel
                  </button>
                  <button
                    className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-600"
                    onClick={() => {
                      setShowConfirmDialog(false);
                      handleLogout();
                    }}
                  >
                    Logout
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
        <div className="w-20 h-15 mb-20 mx-auto my-5 ">
          <NetworkAsset assetName="footer" />
          <p className="text-center text-md mt-1">{version}</p>
        </div>

        {/* Toast Notification */}
        {showToast && (
          <Toast
            message={toastMessage}
            type={toastType}
            duration={3000}
            onClose={() => setShowToast(false)}
            position="bottom-center"
            width="full"
          />
        )}
      </div>
    </div>
  );
}

interface SettingCardLayoutProps {
  title?: string;
  description?: string;
  icon: React.ReactNode;
  onClick: () => void;
  children?: React.ReactNode;
  className?: string;
}

function SettingCardLayout({
  title,
  description,
  icon,
  onClick,
  children,
  className
}: SettingCardLayoutProps) {
  return (
    <div
      className={`w-full max-w-md py-2 cursor-pointer transition ${className}`}
      onClick={onClick}
      tabIndex={0}
      onKeyPress={(e) => {
        if (e.key === "Enter" || e.key === " ") {
          onClick();
        }
      }}
      role="button"
      aria-label="Update your delivery address"
    >
      <div className="flex flex-col">
        <div className="flex flex-row justify-between items-center">
          <div className="flex flex-row items-center">
            <div className="mr-3 text-primary">{icon}</div>
            <div className="">
              {title ? <h2 className="text-sm font-medium">{title}</h2> : null}
              {description ? (
                <p className="text-xs font-light text-gray-500">
                  {description}
                </p>
              ) : null}
            </div>
          </div>
          {title ? (
            <div className="text-typography-300">
              <ChevronRight size={20} />
            </div>
          ) : null}
        </div>
        {children}
      </div>
    </div>
  );
}

function SectionWrapper({
  title,
  children
}: {
  title?: string;
  children: React.ReactNode;
}) {
  return (
    <div className="flex flex-col gap-2 w-full p-2 px-3 rounded-lg bg-white">
      {title ? <p className="text-xs text-typography-700">{title}</p> : null}
      {children}
    </div>
  );
}

export function ErrorBoundary() {
  const navigate = useNavigate();
  return <ErrorBoundaryComponent onClose={() => navigate(-1)} />;
}
