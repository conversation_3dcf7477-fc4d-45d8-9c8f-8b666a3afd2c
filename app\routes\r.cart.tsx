import React, { useEffect, useState, use<PERSON><PERSON>back, useMemo } from "react";
import {
  ActionFunction,
  json,
  LoaderFunction,
  redirect
} from "@remix-run/node";
import {
  useNavigate,
  useFetcher,
  useLoaderData,
  useLocation
} from "@remix-run/react";
// import dayjs from "dayjs";import { DotLottieReact } from '@lottiefiles/dotlottie-react';
// import { DotLottieReact } from "@lottiefiles/dotlottie-react";

import { confirmOrderAPI } from "@services/buyer.service";
import type { Cart, AddonItem, AogList } from "~/types";
import {
  ConfirmOrderPayload,
  PrecheckOrderResponse,
  SellerOrderItem,
  User,
  FulfillmentType
} from "~/types";
import { getSession } from "~/utils/session.server"; // Ensure this is correctly imported

import Button from "~/components/Button";
import SellerOrdersCard from "~/components/SellerOrdersCard";
import SuccessDialog from "~/components/SuccessDialog"; // Import the SuccessDialog component
import Toast from "~/components/Toast"; // Add Toast import
import { roundOff } from "~/utils/roundOff"; // Ensure this utility exists
import { BackNavHeader } from "~/components/BackNavHeader";
import { createClientResponse, requireAuth } from "~/utils/clientReponse";
import { useAppConfigStore } from "~/stores/appConfig.store";
import { parseJWT } from "~/utils/token-utils";
// import { Address, DecodedToken } from "~/types/user";
import { handleWhatsappClick } from "~/components/WhatsappCTA";
import { chooseitemsStore } from "~/stores/chooseitems.store";
import { ChevronRight, ShoppingCart, MapPin } from "lucide-react";
import { isEmptyNullOrUndefinedString } from "~/utils/string";
import SpinnerLoader from "~/components/loader/SpinnerLoader";
import { useCartStore } from "~/stores/cart.store";
import MoQPopup from "~/components/MoQPopup";
import { DecodedToken } from "~/types/user";
import TruncatedText from "~/components/TruncatedText";
import { MnetCoreResponse } from "~/types/Api";
import { cn } from "~/utils/cn";
import PayNow from "~/components/PayNow";
import { precheckResponseToPaymentRequest } from "~/types/payment";
import PaymentMethodSheet, {
  PaymentMethod,
  PaymentCategory
} from "~/components/PaymentMethodSheet";
import DeliveryTimeDisplay from "~/components/DeliveryTimeDisplay";
// import PaymentMethodDisplay from "~/components/PaymentMethodDisplay";
// import ItemDetails from "~/components/chooseitem/ItemDetails";
import { useRequireAuth } from "~/hooks/useRequireAuth";
import { CouponSuccessModal } from "~/components/common/CouponSuccessModel";
import { DefaultCouponModal } from "~/components/common/DefaultCouponModel";
import BottomSheet from "~/components/BottmSheet";
import { applyCouponAPI } from "~/services/cart.service";
import {
  AppliedCouponDTO,
  ApplyCouponPayload,
  CouponDTO
} from "~/types/coupon.types";
import { CouponAppliedBanner } from "~/components/cart/CouponCards";
import { useCouponStore } from "~/stores/coupon.store";
import CouponPageContent from "~/components/coupon/CouponPageContent";
import { userStore } from "~/stores/user.store";
import { useConversionApi } from "~/hooks/useConversionApi";
import { useAnonymousCheck } from "~/hooks/useAnonymousCheck";

interface ActionData {
  success?: boolean;
  error?: string;
}

interface LoaderData {
  // address: Address;
  approxPricing: boolean;
  mobileNumber?: string;
}

export const loader: LoaderFunction = async ({ request }) => {
  const session = await getSession(request.headers.get("Cookie"));
  const access_token = session.get("access_token") as string | null;

  const auth = await requireAuth(request, "", false);
  if (auth && auth.authRequired) {
    return json(auth);
  }

  if (!access_token) {
    return redirect("/login");
  }

  try {
    const decoded = parseJWT(access_token) as DecodedToken;

    if (!decoded || !decoded.userDetails) {
      return redirect("/login");
    }

    const url = new URL(request.url);
    const approxPricingStr = url.searchParams.get("approxPricing");
    let approxPricing = false;
    if (approxPricingStr && approxPricingStr == "true") {
      approxPricing = true;
    }
    // const response = await getAddressAPI(request);
    // const existingAddress = response.data;

    // return createClientResponse<LoaderData, Address>(
    //   request,
    //   {
    //     approxPricing,
    //     mobileNumber: decoded?.userDetails?.mobileNumber,
    //     // address: existingAddress
    //   },
    //   // response
    // );
    return {
      approxPricing,
      mobileNumber: decoded?.userDetails?.mobileNumber
    };
  } catch (err) {
    console.error("Error decoding access_token:", err);
    const auth = await requireAuth(request, "", true);
    if (auth && auth.authRequired) {
      return json(auth);
    }
    return redirect("/login");
  }
};

export const action: ActionFunction = async ({ request }) => {
  const session = await getSession(request.headers.get("Cookie"));
  const access_token = session.get("access_token") as string | null;
  const user: User | null = session.get("user") as User | null;

  const auth = await requireAuth(request, "", false);
  if (auth && auth.authRequired) {
    return json(auth);
  }

  if (!access_token || !user) {
    return json<ActionData>({ error: "Unauthorized" }, { status: 401 });
  }

  const formData = await request.formData();
  const intent = formData.get("intent");

  // Handle coupon application
  if (intent === "applyCoupon") {
    const payloadData = formData.get("payload");

    if (!payloadData || typeof payloadData !== "string") {
      return json<ActionData>(
        { error: "Invalid coupon data" },
        { status: 400 }
      );
    }

    try {
      const payload = JSON.parse(payloadData) as ApplyCouponPayload;

      const response = await applyCouponAPI(payload, request);

      if (response.data && !response.data?.appliedCoupon?.success) {
        return json<ActionData>(
          {
            error:
              response.data?.appliedCoupon?.message || "Failed to apply coupon"
          },
          { status: 500 }
        );
      }

      // Ensure we have valid data before returning
      if (!response.data) {
        return json<ActionData>(
          { error: "No order data returned" },
          { status: 500 }
        );
      }

      return json<ActionData & { order: PrecheckOrderResponse }>({
        success: true,
        order: response.data
      });
    } catch (error) {
      console.error("Error applying coupon:", error);
      return json<ActionData>(
        { error: "Failed to apply coupon" },
        { status: 500 }
      );
    }
  }

  // Handle order confirmation (existing code)
  const orderData = formData.get("order");
  const orderNote = formData.get("orderNote") as string | undefined;

  if (!orderData || typeof orderData !== "string") {
    return json<ActionData>({ error: "Invalid order data" }, { status: 400 });
  }

  let order: PrecheckOrderResponse;
  try {
    order = JSON.parse(orderData);
  } catch (error) {
    console.error("Error parsing order data:", error);
    return json<ActionData>(
      { error: "Invalid order data format" },
      { status: 400 }
    );
  }

  // Prepare the payload for confirmOrderAPI
  const payload: ConfirmOrderPayload = {
    sellerInventoryId: order.sellerInventoryId,
    buyerId: order.buyerId,
    codOpted: order.codSelected,
    codAllowed: order.codAllowed,
    codAmount: order.codAmount,
    deliveryDate: order.deliveryDate,
    deliveryCharges: order.deliveryCharges,
    discountAmount: order.discountAmount,
    walletAmount: order.walletAmount,
    existingOrderGroupId: order.existingOrderGroupId,
    cartKey: order.cartKey,
    items: order.items.map((item) => ({
      sellerId: item.sellerId,
      inventoryId: item.inventoryId,
      sellerItemId: item.sellerItemId,
      pricePerUnit: item.pricePerUnit,
      quantity: item.quantity,
      amount: item.amount,
      addOnItemDtos: item.addOnItemDtos
    })),
    sellerMessage: orderNote
  };

  if (order.preconfirmUid) {
    payload.preconfirmUid = order.preconfirmUid;
  }

  try {
    console.log("Confirming order with payload:", JSON.stringify(payload));

    const response = await confirmOrderAPI(payload, request);
    console.log("Order confirmed successfully.");

    if (!response.data.success) {
      return json<ActionData>(
        { error: response.data.error?.message },
        { status: 500 }
      );
    }

    return createClientResponse<
      ActionData,
      MnetCoreResponse<PrecheckOrderResponse>
    >(request, { success: true }, response);
  } catch (error) {
    console.error("Error confirming order:", error);
    return json<ActionData>(
      { error: "Failed to confirm order" },
      { status: 500 }
    );
  }
};

// Add this component before the main Cart component
const EmptyCart: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className="flex flex-col items-center justify-center h-[80vh] px-4">
      <ShoppingCart className="w-16 h-16 text-neutral-400 mb-4" />
      <h2 className="text-xl font-semibold text-typography-800 mb-2">
        Your cart is empty
      </h2>
      <p className="text-sm text-typography-500 text-center mb-6">
        {`Looks like you haven't added any items to your cart yet.`}
      </p>
      <Button
        onClick={() => navigate("/home/<USER>")}
        className="p-3 rounded-xl flex justify-between align-center gap-2 bg-primary hover:bg-primary-600 text-white"
      >
        <span>Start Shopping</span>
        <ChevronRight className="self-center" size={"1.25rem"} />
      </Button>
    </div>
  );
};

const Cart: React.FC = () => {
  const navigate = useNavigate();
  const fetcher = useFetcher<ActionData>();
  const precheckFetcher = useFetcher();
  const couponFetcher = useFetcher<
    ActionData & { order?: PrecheckOrderResponse }
  >();
  const loader = useLoaderData<LoaderData>();
  const location = useLocation();
  const {
    cart: currentCart,
    clearCart,
    setCart,
    orderNote,
    setOrderNote,
    setShowNoteFeature,
    setCartOrderItemsMap,
    setCartClientType,
    setPrecheckResponse
  } = useCartStore((state) => state);

  useRequireAuth();

  const { requireRealAuth } = useAnonymousCheck();

  // Initialize order state from location or localStorage
  const [order, setOrder] = useState<PrecheckOrderResponse | null>(() => {
    const locationOrder = location.state?.order as PrecheckOrderResponse;
    if (locationOrder) {
      // Store in localStorage when available from location
      if (typeof window !== "undefined" && window.localStorage) {
        localStorage.setItem("currentOrder", JSON.stringify(locationOrder));
      }

      return locationOrder;
    }

    // Try to get from localStorage if not in location state
    const savedOrder =
      typeof window !== "undefined" && window.localStorage
        ? localStorage.getItem("currentOrder")
        : null;
    return savedOrder ? JSON.parse(savedOrder) : null;
  });

  const [placedOrder, setPlacedOrder] = useState<PrecheckOrderResponse | null>(
    null
  );
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [showToast, setShowToast] = useState(false);
  const [tostType, setTostType] = useState<
    "success" | "error" | "warning" | "info" | "itemLimited"
  >("error");
  const [isSubmittingOrder, setIsSubmittingOrder] = useState(false);
  const { appSource, networkConfig } = useAppConfigStore((state) => state);
  const { initializeCoupons, selectedCoupon, selectCoupon } = useCouponStore(
    (state) => state
  );

  const { itemOptionsData, selectedParentCategory, categoryTypeList } =
    chooseitemsStore((state) => state);
  const [showLoader, setShowLoader] = useState(true);
  const [showMoq, setShowMoq] = useState(false);
  const [showMov, setShowMov] = useState(false);
  const [showServiceAreaToast, setShowServiceAreaToast] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>("cod");
  const [paymentCategory, setPaymentCategory] =
    useState<PaymentCategory>("online"); // for restaurant only always online
  const [showPaymentSheet, setShowPaymentSheet] = useState(false);
  const [showCouponSuccessModal, setShowCouponSuccessModal] = useState(false);
  const [showDefaultCouponModal, setShowDefaultCouponModal] = useState(false);
  const [defaultCouponData, setDefaultCouponData] = useState<
    CouponDTO | undefined
  >(undefined);
  const [appliedCouponData, setAppliedCouponData] = useState<
    AppliedCouponDTO | undefined
  >(undefined);
  const [isAlreadyApplied, setIsAlreadyApplied] = useState(false);
  const [removedCoupon, setRemovedCoupon] = useState(false);
  const [showCouponModal, setShowCouponModal] = useState(false);
  const { decodedToken } = userStore((state) => state);

  const { trackInitiateCheckout } = useConversionApi();

  // Add fulfillment type state
  // TODO: preset the fulfillment type from cart to rsrp page
  const [fulfillmentType, setFulfillmentType] = useState<FulfillmentType>(
    order?.fulfillmentType ||
      (order?.takeAwayEnabled ? "TAKE_AWAY" : "DELIVERY")
  );
  const [takeAwayEnabled, setTakeAwayEnabled] = useState(
    order?.fulfillmentType === "TAKE_AWAY" || order?.takeAwayEnabled
  );

  const coupons = order?.applicableCoupons;

  useEffect(() => {
    setTakeAwayEnabled(fulfillmentType === "TAKE_AWAY");
  }, [fulfillmentType]);

  // initialize coupons
  useEffect(() => {
    if (coupons) {
      initializeCoupons(coupons);
    }
  }, [coupons, initializeCoupons]);

  // Enable note feature by default
  // Enable note feature by default
  useEffect(() => {
    setShowNoteFeature(true);
    setCartClientType("restaurant");
  }, [setShowNoteFeature, setCartClientType]);

  const precheckItemMap = useMemo(() => {
    const map = new Map<number, SellerOrderItem>();
    order?.items?.forEach((item) => {
      map.set(item.sellerItemId, item);
    });
    return map;
  }, [order]);

  useEffect(() => {
    setCartOrderItemsMap(precheckItemMap);
    if (order) {
      setPrecheckResponse(order);
    }
  }, [precheckItemMap, setCartOrderItemsMap, setPrecheckResponse, order]);

  // useEffect(() => {
  //   if (order?.codAllowed) {
  //     setPaymentMethod("cod");
  //     setPaymentCategory("offline");
  //   } else {
  //     setPaymentMethod("online");
  //     setPaymentCategory("online");
  //   }
  // }, [order?.codAllowed]);

  // Add effect to handle return from address page
  useEffect(() => {
    const isReturningFromAddress = location.state?.fromAddress;
    if (isReturningFromAddress && order) {
      // Clear the state flag
      window.history.replaceState(
        { ...window.history.state, fromAddress: undefined },
        ""
      );

      // Trigger precheck API
      handleItemUpdate("component-mount");
    }
  }, [location.state]);

  const checkMoqMov = () => {
    if (order?.insufficientOrderQuantity) {
      setShowMoq(true);
      setShowMov(false);
      return true;
    } else if (order?.insufficientOrderValue) {
      setShowMov(true);
      setShowMoq(false);
      return true;
    } else {
      setShowMoq(false);
      setShowMov(false);
      return false;
    }
  };

  // Update localStorage when order changes
  useEffect(() => {
    if (order) {
      localStorage.setItem("currentOrder", JSON.stringify(order));
    } else {
      localStorage.removeItem("currentOrder");
    }
  }, [order]);

  // Set coupon data
  useEffect(() => {
    if (
      order?.applicableCoupons?.[0] &&
      !order?.appliedCoupon &&
      !order?.appliedCoupon?.success
    ) {
      setDefaultCouponData(order?.applicableCoupons?.[0]);
      console.log("couponData", defaultCouponData, order?.applicableCoupons);

      setShowDefaultCouponModal(true);
    } else {
      setShowDefaultCouponModal(false);
    }

    if (order?.appliedCoupon?.success) {
      setAppliedCouponData(order?.appliedCoupon);
      selectCoupon(order?.appliedCoupon?.coupon?.id);
      setShowCouponSuccessModal(true);
    } else if (order?.appliedCoupon && !order?.appliedCoupon?.success) {
      setErrorMessage(
        order?.appliedCoupon?.message || "Failed to apply coupon"
      );
      setShowToast(true);
      setTostType("info");
      setIsAlreadyApplied(false);
      setShowCouponSuccessModal(false);
      setAppliedCouponData(undefined);
      selectCoupon(0);
    } else {
      setAppliedCouponData(undefined);
      selectCoupon(0);
    }
  }, [order, precheckFetcher.state]);

  // Helper function to process addon data from order items
  const processAddonData = (
    addonItemDtos: Array<AddonItem & { groupId?: number; groupName?: string }>
  ): {
    flatAddons: AddonItem[];
    aogList: AogList[];
  } => {
    // Create flat addons list from response
    const flatAddons: AddonItem[] = addonItemDtos.map((addon) => ({
      id: addon.id,
      name: addon.name,
      price: addon.price,
      qty: addon.qty,
      seq: addon.seq || 0,
      diet: addon.diet || null,
      sId: addon.sId
    }));

    // Build aogList structure from flatAddons
    // Group addons by their groupId
    const addonsByGroup: Record<string | number, AogList> = {};

    addonItemDtos.forEach((addon) => {
      const groupId = addon.groupId?.toString() || "default";

      if (!addonsByGroup[groupId]) {
        addonsByGroup[groupId] = {
          id: Number(groupId) || 0,
          name: addon.groupName || "Add-ons",
          minSelect: 0,
          maxSelect: 10,
          description: "",
          seq: 0,
          addOnItemList: []
        };
      }

      addonsByGroup[groupId].addOnItemList.push({
        id: addon.id,
        name: addon.name,
        price: addon.price,
        qty: addon.qty,
        seq: addon.seq || 0,
        diet: addon.diet || null,
        sId: addon.sId
      });
    });

    // Convert grouped addons to array
    const aogList = Object.values(addonsByGroup);

    return { flatAddons, aogList };
  };

  // Initialize cart from order on component mount
  useEffect(() => {
    if (order && order.items) {
      const initialCart: Cart = {};
      order.items.forEach((item) => {
        if (item.quantity > 0) {
          // Check if item has addon information
          const hasAddonInfo =
            item.addOnItemDtos && item.addOnItemDtos.length > 0;

          let flatAddons: AddonItem[] = [];
          let aogList: AogList[] = [];

          if (hasAddonInfo && item.addOnItemDtos) {
            // Process addon data
            const {
              flatAddons: processedFlatAddons,
              aogList: processedAogList
            } = processAddonData(
              item.addOnItemDtos as AddonItem[] &
                { groupId?: number; groupName?: string }[]
            );

            flatAddons = processedFlatAddons;
            aogList = processedAogList;
          }

          initialCart[item.sellerItemId] = {
            itemId: item.sellerItemId,
            qty: item.quantity,
            amount: item.amount,
            cartKey: order.cartKey,
            aogList: aogList.length > 0 ? aogList : undefined,
            flatAddons: flatAddons.length > 0 ? flatAddons : undefined,
            variationId: item.variationId
          };
        }
      });

      // Store cart in localStorage
      localStorage.setItem("cartKey", order.cartKey || "");

      setCart(initialCart, order.cartKey);

      // if (
      //   order?.applicableCoupons?.[0] &&
      //   !order?.appliedCoupon &&
      //   !order?.appliedCoupon?.success
      // ) {
      //   setCouponData(order?.applicableCoupons?.[0]);
      // }
      // if (order?.appliedCoupon) {
      //   setAppliedCouponData(order?.appliedCoupon);
      // }
    } else {
      // Try to restore cart from localStorage if order is not available
      const savedCartKey = localStorage.getItem("cartKey");
      const savedCart = localStorage.getItem(`cart_${savedCartKey}`);

      if (savedCart && savedCartKey) {
        const parsedCart = JSON.parse(savedCart);
        setCart(parsedCart, savedCartKey);
      }
    }
  }, []);

  // Load saved order note from localStorage
  useEffect(() => {
    if (order?.cartKey) {
      const savedNote = localStorage.getItem(`orderNote_${order.cartKey}`);
      if (savedNote) {
        setOrderNote(savedNote, order.cartKey);
      }
    }
  }, [order?.cartKey, setOrderNote]);

  // Clear localStorage when order is successfully placed
  useEffect(() => {
    if (fetcher.state === "idle" && fetcher.data?.success) {
      localStorage.removeItem("currentOrder");
      localStorage.removeItem("currentCart");
      localStorage.removeItem("cartKey");

      // Also remove the order note
      if (order?.cartKey) {
        localStorage.removeItem(`orderNote_${order.cartKey}`);
      }
    }
  }, [fetcher.state, fetcher.data, order?.cartKey]);

  // Update order and cart when precheck response is received
  useEffect(() => {
    if (precheckFetcher.data && precheckFetcher.state === "idle") {
      try {
        const newOrder = precheckFetcher.data as PrecheckOrderResponse;
        if (newOrder) {
          setOrder(newOrder);

          // Create new cart object based on precheck response
          const updatedCart: Cart = {};

          // Get current cart to preserve addon information when needed
          const currentCart = useCartStore.getState().cart;

          newOrder.items.forEach((item) => {
            if (item.quantity > 0) {
              // Check if item has addon information from the response
              const hasAddonInfo =
                item.addOnItemDtos && item.addOnItemDtos.length > 0;

              // Create flat addons list from response or preserve existing ones
              let flatAddons: AddonItem[] = [];
              let aogList: AogList[] = [];

              if (hasAddonInfo && item.addOnItemDtos) {
                // Process addon data
                const {
                  flatAddons: processedFlatAddons,
                  aogList: processedAogList
                } = processAddonData(
                  item.addOnItemDtos as AddonItem[] &
                    { groupId?: number; groupName?: string }[]
                );

                flatAddons = processedFlatAddons;
                aogList = processedAogList;
              } else if (currentCart[item.sellerItemId]?.flatAddons) {
                // Preserve existing flat addons if not in response
                flatAddons = currentCart[item.sellerItemId].flatAddons || [];
                aogList = currentCart[item.sellerItemId].aogList || [];
              }

              updatedCart[item.sellerItemId] = {
                itemId: item.sellerItemId,
                qty: item.quantity,
                amount: item.amount,
                cartKey: newOrder.cartKey,
                aogList: aogList.length > 0 ? aogList : undefined,
                flatAddons: flatAddons.length > 0 ? flatAddons : undefined,
                variationId: item.variationId
              };
            }
          });

          // Update cart store with new cart data
          setCart(updatedCart, newOrder.cartKey);
        }
      } catch (error) {
        console.error("Error updating order:", error);
        setErrorMessage("Failed to update order. Please try again.");
        setShowToast(true);
      }
    }
  }, [precheckFetcher.state, precheckFetcher.data]);

  // Handle item updates and trigger precheck API
  const handleItemUpdate = async (
    action?: "add" | "remove" | "component-mount",
    couponData?: CouponDTO,
    currentFulfillmentType?: FulfillmentType
  ) => {
    if (!order?.items || !order.cartKey) {
      console.error("Order or items not available");
      return;
    }

    try {
      // Get the current cart state directly from the store
      const cart = useCartStore.getState().cart;

      // If coupon Update is true, then use the couponId from the couponData
      // Otherwise, use the couponId from the order.appliedCoupon
      let couponId =
        action === "add" && couponData
          ? couponData?.id
          : appliedCouponData?.coupon?.id || null;

      if (action === "remove") {
        couponId = null;
      }

      if (action === "component-mount") {
        couponId = order?.appliedCoupon?.coupon?.id || null;
        setIsAlreadyApplied(true);
        setRemovedCoupon(true);
      }

      // Submit precheck request
      precheckFetcher.submit(
        {
          intent: "precheck",
          cart: JSON.stringify(cart),
          deliveryDate: order.deliveryDate,
          sellerId: order.sellerId,
          sellerDataId: order.sellerInventoryId,
          codAllowed: order.codSelected,
          existingOrderGroupId: order.existingOrderGroupId,
          cartKey: order.cartKey,
          preconfirmUid: order.preconfirmUid,
          sellerMessage: orderNote,
          couponId: couponId,
          fulfillmentType: currentFulfillmentType || fulfillmentType
        },
        { method: "post", action: "/home/<USER>" }
      );
    } catch (error) {
      console.error("Error updating cart:", error);
      setErrorMessage("Failed to update cart. Please try again.");
      setShowToast(true);
    }
  };

  // Handle actionData responses
  useEffect(() => {
    if (fetcher.state === "submitting") {
      setIsSubmittingOrder(true);
    }

    if (fetcher.state === "idle" && fetcher.data) {
      if (fetcher.data.error) {
        setErrorMessage(fetcher.data.error);
        setShowToast(true);
        setShowServiceAreaToast(true);
        setIsSubmittingOrder(false);
      } else if (fetcher.data.success) {
        if (order) {
          // Capture the placed order details before clearing
          setPlacedOrder(order);

          // Clear the current order and cart
          localStorage.removeItem("order");
          clearCart(order.cartKey);
          setOrder(null);

          // Show success dialog
          setShowSuccessDialog(true);
          // Only reset submission state after dialog is shown
          setIsSubmittingOrder(false);
        } else {
          // Edge case: success but no order in state
          console.warn("Order data missing after successful confirmation.");
          setIsSubmittingOrder(false);
        }
      }
    }
  }, [fetcher.state, fetcher.data, order]);

  // Add new useEffect for loader timing
  useEffect(() => {
    if (!order && !placedOrder) {
      const timer = setTimeout(() => {
        setShowLoader(false);
      }, 30000);
      return () => clearTimeout(timer);
    }
  }, [order, placedOrder]);

  const newOrderItems = useCallback(
    () =>
      order?.items.filter((item) => {
        const currentQty = currentCart[item.sellerItemId]?.qty || 0;
        const orderedQty = item.availableCartItem?.orderedQty || 0;
        return (
          (!item.isSoldOut || item.quantity > 0) && currentQty > orderedQty
        );
      }),
    [order?.items, currentCart]
  );

  const disablePlaceOrderButton = useCallback(() => {
    return (
      newOrderItems()?.length === 0 ||
      isSubmittingOrder ||
      fetcher.state === "submitting" ||
      !order?.defaultAddress?.buyerInServiceArea
    );
  }, [newOrderItems, isSubmittingOrder, fetcher.state]);

  const handlePlaceOrder = () => {
    if (!order) {
      setErrorMessage("No order data available.");
      setShowToast(true);
      return;
    }

    const isAnonymous = requireRealAuth();
    if (isAnonymous) {
      navigate("/home/<USER>", { replace: true });
      return;
    }

    if (!order?.defaultAddress?.buyerInServiceArea) {
      setShowServiceAreaToast(true);
      return;
    }

    if (checkMoqMov()) {
      return;
    }

    if (isSubmittingOrder) {
      return;
    }

    trackInitiateCheckout({
      eventData: {
        currency: "INR",
        value: order.totalAmount,
        contentIds: order.items.map((item) => item.sellerItemId.toString())
      },
      eventId: `EVT-${Date.now()}-${Math.random()
        .toString(36)
        .substring(2, 8)}`,
      eventTime: Date.now()
    });

    // filter the sold out items with zero quantity
    const filteredOrder = {
      ...order,
      items: order.items.filter(
        (item) => item.quantity > 0 && item.isSoldOut === false
      )
    };

    // Submit the order via fetcher.submit with the order note
    fetcher.submit(
      {
        order: JSON.stringify(filteredOrder),
        orderNote
      },
      { method: "post", action: "/r/cart" }
    );
  };

  // const handleWhatsappRedirect = () => {
  //   handleWhatsappClick(networkConfig?.wabMobileNumber || "", "Track order");
  // };

  const handleRedirect = () => {
    setShowSuccessDialog(false);
    navigate("/home/<USER>/orders", { replace: true });
  };

  // Replace the existing no-order check with this new logic

  // console.log(
  //   "selectedParentCategory",
  //   selectedParentCategory,
  //   itemOptionsData?.sellerId
  // );

  const handleBack = () => {
    if (
      itemOptionsData?.availableItems &&
      (categoryTypeList.includes("L2") || categoryTypeList.includes("L3"))
    ) {
      navigate(
        `/home/<USER>
        { replace: true }
      );
    } else {
      navigate("/home/<USER>", { replace: true });
    }
  };

  // Add this check right after the BackNavHeader
  const isCartEmpty = !order?.items || order.items.length === 0;

  // show the address btn
  const showAddressBtn =
    (order?.defaultAddress &&
      isEmptyNullOrUndefinedString(order?.defaultAddress.address || "")) ||
    (parseInt(order?.defaultAddress?.latitude || "0") === 0 &&
      parseInt(order?.defaultAddress?.longitude || "0") === 0);

  // Handle payment method selection
  const handleSelectPaymentMethod = (
    method: PaymentMethod,
    category: PaymentCategory
  ) => {
    setPaymentMethod(method);
    setPaymentCategory(category);
  };

  // Add a function to handle coupon application
  const handleApplyCoupon = useCallback(
    (coupon: CouponDTO) => {
      if (!order?.preconfirmUid || !coupon) return;

      // Close the modal first to avoid concurrent state updates
      setShowDefaultCouponModal(false);
      setIsAlreadyApplied(false);
      // Use setTimeout to defer the API call to the next tick
      setTimeout(() => {
        handleItemUpdate("add", coupon);
      }, 0);
    },
    [order?.preconfirmUid, defaultCouponData]
  );

  // Add a function to handle coupon application
  const handleRemoveCoupon = useCallback(() => {
    if (!order?.preconfirmUid || !appliedCouponData?.coupon) return;

    handleItemUpdate("remove");
    // Show success modal and close default coupon modal
    setShowDefaultCouponModal(false);
    setAppliedCouponData(undefined);
    selectCoupon(0);
    setRemovedCoupon(true);
  }, [order?.preconfirmUid, appliedCouponData?.coupon, couponFetcher]);

  // Update the useEffect to handle coupon application response
  useEffect(() => {
    if (couponFetcher.state === "idle" && couponFetcher.data) {
      if (couponFetcher.data.error) {
        setErrorMessage(couponFetcher.data.error);
        setShowToast(true);
      } else if (couponFetcher.data.success && couponFetcher.data.order) {
        // Update the order with the new coupon data
        setOrder(couponFetcher.data.order as PrecheckOrderResponse);
        setAppliedCouponData(undefined);
        selectCoupon(0);
      }
    }
  }, [couponFetcher.state, couponFetcher.data]);

  // Handle fulfillment type change
  const handleFulfillmentChange = (option: FulfillmentType) => {
    setFulfillmentType(option);
    handleItemUpdate(undefined, undefined, option);
  };

  if (showCouponModal) {
    return (
      <CouponPageContent
        handleApplyCoupon={(coupon) => {
          handleApplyCoupon(coupon);
          setShowCouponModal(false);
        }}
        handleBack={() => {
          setShowCouponModal(false);
        }}
      />
    );
  }

  return (
    <div className="flex flex-col h-screen bg-[#F2F4F9] fixed inset-0">
      {/* Header Section */}
      <BackNavHeader
        buttonText="My Cart"
        handleBack={handleBack}
        className="min-h-12 px-3"
      />

      {!takeAwayEnabled ? (
        <DeliveryTimeDisplay time={order?.estDeliveryTime || "30 minutes"} />
      ) : null}

      {/* Show empty cart state when cart is empty */}
      {isCartEmpty ? (
        <EmptyCart />
      ) : (
        <>
          {/* Show loader while fetching initial data */}
          {!order && !placedOrder && (
            <>
              {showLoader ? (
                <SpinnerLoader size={12} loading={true} />
              ) : (
                <div className="flex items-center justify-center h-screen">
                  <div className="p-4 text-red-700 text-center rounded">
                    No order details found. Please try again.
                  </div>
                </div>
              )}
            </>
          )}

          {/* Show loading indicator during precheck */}
          {(precheckFetcher.state !== "idle" ||
            couponFetcher.state !== "idle") && (
            <SpinnerLoader size={12} loading={true} />
          )}

          {/* Coupon Applied Banner */}

          <CouponAppliedBanner
            discountValue={appliedCouponData?.coupon?.discountValue || 0}
            visible={appliedCouponData?.success ? true : false}
          />

          {/* Seller Orders */}
          <div className="flex-grow h-full">
            {order && (
              <SellerOrdersCard
                approxPricing={loader.approxPricing}
                orderDetails={order}
                handleBack={handleBack}
                onItemUpdate={handleItemUpdate}
                appliedCoupon={appliedCouponData?.coupon}
                onRemoveCoupon={handleRemoveCoupon}
                showCoupon={order?.applicableCoupons?.[0] ? true : false}
                onApplyCoupon={() => {
                  // setShowDefaultCouponModal(true);
                  // setRemovedCoupon(false);
                  setShowCouponModal(true);
                }}
                selectedFulfillmentType={fulfillmentType}
                onFulfillmentChange={handleFulfillmentChange}
              />
            )}
          </div>

          {/* Error Message */}
          {errorMessage && showToast && (
            <Toast
              message={errorMessage}
              type={tostType || "error"}
              duration={5000}
              onClose={() => setShowToast(false)}
              position="bottom-center"
              showIcon
              showCloseButton={true}
              width="full"
              autoClose={true}
            /> // <div className="p-4 bg-red-100 text-red-700 text-center">
            //   {errorMessage}
            // </div>
          )}

          {/* PLACE ORDER Button */}
          {order && (
            <div className="fixed w-full bottom-0 right-0 left-0 flex flex-col items-center justify-between bg-white shadow-md">
              {/* approx pricing banner */}
              {order.insufficientOrderQuantity && (
                <div className="w-full rounded-t-lg bg-secondary-50 h-12 flex justify-center items-center px-2">
                  <p className="text-xs font-normal text-secondary text-center">
                    Online ordering for
                    <span className="font-semibold"> {order?.sellerName} </span>
                    is only valid for orders
                    <span className="font-semibold">
                      {" "}
                      above {order?.minOrderQty} kg.{" "}
                    </span>
                    <br />
                    Please add atleast{" "}
                    <span className="font-semibold">
                      {" "}
                      {(order?.minOrderQty || 0).toFixed(2)} kg{" "}
                    </span>{" "}
                    more to proceed.
                  </p>
                </div>
              )}
              {order.insufficientOrderValue && (
                <div className="w-full rounded-t-lg bg-secondary-50 h-12 flex justify-center items-center px-2">
                  <p className="text-xs font-normal text-secondary text-center">
                    <span className="font-semibold"> {order?.sellerName} </span>
                    requires a minimum order of
                    <span className="font-semibold">
                      {" "}
                      above ₹{order?.minOrderValue}{" "}
                    </span>{" "}
                    for online orders. <br />
                    Please add at least{" "}
                    <span className="font-semibold">
                      {" "}
                      ₹{(order?.minOrderValue - order.totalAmount).toFixed(
                        2
                      )}{" "}
                    </span>{" "}
                    more to proceed.
                  </p>
                </div>
              )}

              {order?.defaultAddress && showAddressBtn ? (
                <Button
                  className="my-4 p-3 rounded-xl w-11/12 bg-primary hover:bg-primary-600 text-white"
                  onClick={() => {
                    const isAnonymous = requireRealAuth();
                    if (isAnonymous) {
                      navigate("/home/<USER>", { replace: true });
                      return;
                    }
                    navigate("/changeaddress?redirectTo=/r/cart", {
                      state: {
                        address: order?.defaultAddress,
                        isEdit: true,
                        from: "/r/cart",
                        returnTo: "/r/cart"
                      }
                    });
                  }}
                >
                  Confirm address
                </Button>
              ) : (
                <div className="w-full flex flex-col items-center justify-between p-4 bg-white shadow-md">
                  {/* Address Section */}
                  {!takeAwayEnabled ? (
                    <div
                      className={cn(
                        "flex flex-col w-full gap-2 justify-between mb-3 border-b border-neutral-200 pb-3",
                        !order.defaultAddress.buyerInServiceArea &&
                          "text-red-500"
                      )}
                    >
                      <div className="flex items-center w-full gap-2 justify-between">
                        <div className="flex items-center gap-2">
                          <MapPin className="w-4 h-4 text-primary" />
                          <div className="flex gap-1">
                            {order?.defaultAddress?.name && (
                              <div
                                className={cn(
                                  "flex gap-1 text-xs tracking-wide",
                                  !order.defaultAddress.buyerInServiceArea &&
                                    "text-red-500 font-bold bg-orange-50 px-2 py-1 rounded-md"
                                )}
                              >
                                <span>
                                  {order.defaultAddress.buyerInServiceArea
                                    ? "Delivery at "
                                    : "Not Deliverable at "}
                                </span>
                                <span className="font-semibold">
                                  {order.defaultAddress.name.toLocaleUpperCase()}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                        <Button
                          className="text-primary text-xs"
                          type="button"
                          onClick={() => {
                            const isAnonymous = requireRealAuth();
                            if (isAnonymous) {
                              navigate("/home/<USER>", { replace: true });
                              return;
                            }
                            navigate(
                              "/select-address?flowType=select-address&returnTo=/r/cart",
                              {
                                state: {
                                  from: "/r/cart",
                                  returnTo: "/r/cart",
                                  flowType: "select-address"
                                }
                              }
                            );
                          }}
                        >
                          CHANGE
                        </Button>
                      </div>
                      <TruncatedText
                        className={cn(
                          "text-xs font-light tracking-wide text-typography-400 w-[80vw]",
                          !order.defaultAddress.buyerInServiceArea &&
                            "text-red-500"
                        )}
                        text={order?.defaultAddress.address}
                      />
                    </div>
                  ) : null}
                  <div className="flex items-center w-full gap-2 justify-between">
                    {/* <div className="flex flex-col gap-1 w-[50vw]">
                      <div className="flex gap-2 items-center object-center">
                        <p className="text-xs font-light tracking-wide text-typography-500 self-center">
                          PAY USING
                        </p>
                      </div> */}

                    {/* Replace the inline button with the PaymentMethodDisplay component */}
                    {/* <PaymentMethodDisplay
                        paymentMethod={paymentMethod}
                        onClick={() => {
                          // setShowPaymentSheet(true);
                          console.log("paymentMethod", paymentMethod);
                        }}
                      /> */}
                    {/* </div> */}

                    {paymentCategory === "online" &&
                    order &&
                    order.balancePayableAmount > 0 ? (
                      <PayNow
                        paymentRequest={precheckResponseToPaymentRequest(order)}
                        buttonClassName={`p-3 rounded-xl flex justify-center align-center gap-1 w-full ${
                          disablePlaceOrderButton()
                            ? "bg-neutral-500 text-neutral-800 cursor-not-allowed"
                            : "bg-primary hover:bg-primary-600 text-white"
                        }`}
                        buttonText={`PAY ₹${roundOff(
                          order.balancePayableAmount,
                          true
                        )}`}
                        useDeposit={true}
                        preconfirmUid={order.preconfirmUid}
                        disabled={disablePlaceOrderButton()}
                        onSuccess={() => {
                          // Handle successful payment
                          localStorage.removeItem("order");
                          clearCart(order.cartKey);
                          setOrder(null);
                          setShowSuccessDialog(true);
                        }}
                        onClose={() => {
                          // Handle closing of payment modal
                          setErrorMessage(null);
                        }}
                        retryCallback={() => {
                          // Navigate to home/rsrp on retry
                          navigate("/home/<USER>");
                        }}
                        customerInfo={{
                          name: decodedToken?.userDetails?.userName || "",
                          email: "",
                          contact: decodedToken?.userDetails?.mobileNumber || ""
                        }}
                      />
                    ) : (
                      <Button
                        type="button"
                        onClick={handlePlaceOrder}
                        disabled={disablePlaceOrderButton()}
                        className={`p-3 rounded-xl flex justify-between align-center gap-1 w-full ${
                          disablePlaceOrderButton()
                            ? "bg-neutral-500 text-neutral-800 cursor-not-allowed"
                            : "bg-primary hover:bg-primary-600 text-white"
                        }`}
                      >
                        <div className="flex flex-col gap-1 self-center">
                          {/* <p
                            className={`text-[0.625rem] font-light tracking-wide whitespace-nowrap
                              ${
                                disablePlaceOrderButton()
                                  ? "text-neutral-800"
                                  : "text-white"
                              }
                              self-start`}
                          >
                            <span>TOTAL </span>
                          </p> */}
                        </div>
                        <div className="w-full flex  justify-between">
                          <span className="flex flex-wrap w-fit text-right tracking-wider">
                            {isSubmittingOrder || fetcher.state === "submitting"
                              ? "PLACING ..."
                              : "PLACE ORDER"}
                          </span>
                          <ChevronRight
                            className="self-center"
                            size={"1.25rem"}
                          />
                        </div>
                      </Button>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}
        </>
      )}

      {/* Success Dialog */}
      {showSuccessDialog && (
        <div className="p-16">
          <SuccessDialog
            title="Order Placed"
            message="Thankyou for placing the order"
            buttonText="Okay!"
            buttonType="primary"
            onRedirect={
              // appSource === "whatsappchat"
              //   ? handleWhatsappRedirect
              //   : handleRedirect
              handleRedirect
            }
            countdownStart={5} // Set countdown to 5 seconds
          />
        </div>
      )}

      {/* MOQ/MOV Popup */}
      <MoQPopup
        visible={Boolean(
          order?.items && order?.items.length > 0 && (showMoq || showMov)
        )}
        onClose={() => {
          setShowMoq(false);
          setShowMov(false);
        }}
        qty={order?.minOrderQty || 0}
        currentQty={Object.values(currentCart).reduce((acc, item) => {
          const itemDetails = precheckItemMap.get(item.itemId);
          if (itemDetails && itemDetails.weightFactor) {
            return acc + item.qty * itemDetails.weightFactor;
          }
          return acc + item.qty;
        }, 0)}
        value={order?.minOrderValue || 0}
        showMoq={showMoq}
        showMov={showMov}
      />

      {/* Add Toast component */}
      {showServiceAreaToast && (
        <Toast
          type="itemLimited"
          message={
            errorMessage ||
            `${order?.sellerName} is not available at your location.`
          }
          onClose={() => setShowServiceAreaToast(false)}
          duration={3000}
          position="bottom-center"
          width="full"
        />
      )}

      {/* Payment Method Sheet */}
      <PaymentMethodSheet
        isOpen={showPaymentSheet}
        onClose={() => setShowPaymentSheet(false)}
        onSelectPaymentMethod={handleSelectPaymentMethod}
        selectedMethod={paymentMethod}
        amount={order?.totalAmount || 0}
      />

      {appliedCouponData && !isAlreadyApplied && (
        <CouponSuccessModal
          open={showCouponSuccessModal}
          onClose={() => {
            setShowCouponSuccessModal(false);
            setIsAlreadyApplied(true);
          }}
          couponData={appliedCouponData.coupon}
        />
      )}
      {defaultCouponData && !removedCoupon && (
        <BottomSheet
          isOpen={showDefaultCouponModal}
          onClose={() => {
            setShowDefaultCouponModal(false);
            setIsAlreadyApplied(false);
            setRemovedCoupon(true);
          }}
          showSwipeIndicator={false}
        >
          <DefaultCouponModal
            open={showDefaultCouponModal}
            onClose={() => handleApplyCoupon(defaultCouponData)}
            couponData={defaultCouponData}
          />
        </BottomSheet>
      )}
    </div>
  );
};

export default Cart;
