import { type FC } from "react";
import dayjs from "dayjs";
import { formatCurrency, IsValidTime } from "../utils/format";
import type { Order, OrderItem, OrderStatus } from "~/types";
import {
  Bell,
  Check,
  Package,
  Clock,
  Truck,
  CheckCircle2,
  ChevronRight,
  CircleX
} from "lucide-react";
import { useAppConfigStore } from "~/stores/appConfig.store";
import CustomImage from "./CustomImage";
import PayNow from "./PayNow";
import { orderToPaymentRequest } from "~/types/payment";
import { useNavigate } from "@remix-run/react";
import { DietaryImage } from "./common/DietaryImage";
interface OrderCardProps {
  orderDetails: Order;
  onPress?: () => void;
}

const getStatusStyle = (status: OrderStatus) => {
  switch (status) {
    case "Created":
      return "text-[#7D68FF] italic bg-[#F5F4FA]";
    case "Accepted":
      return "text-[#F48A1C] italic bg-[#FEF3E1]";
    case "Packed":
      return "text-[#FF752C]  italic bg-[#FFF1EA]";
    case "Assigned":
      return "text-pink-400 italic bg-pink-50";
    case "PickedUp":
    case "Dispatched":
      return "text-[#00B6BE] italic bg-[#DFF9F8]";
    case "Delivered":
      return "text-primary italic ";
    case "Cancelled":
      return "text-secondary italic ";
    default:
      return "text-gray-600 italic bg-gray-50";
  }
};

const getStatusIcon = (status: OrderStatus) => {
  switch (status) {
    case "Created":
      return <Bell size={16} className="" />;
    case "Accepted":
      return <Check size={16} className="" />;
    case "Packed":
      return <Package size={16} className="" />;
    case "Assigned":
      return <Clock size={16} className="" />;
    case "PickedUp":
    case "Dispatched":
      return <Truck size={16} className="" />;
    case "Delivered":
      return <CheckCircle2 size={16} className=" fill-primary text-white" />;
    case "Cancelled":
      return <CircleX size={16} className="fill-secondary text-white" />;
    default:
      return null;
  }
};

const getDisplayStatus = (status: OrderStatus): string => {
  switch (status) {
    case "PickedUp":
      return "Out for Delivery";
    case "Dispatched":
      return "Out for Delivery";
    case "Assigned":
      return "Waiting for Pickup";
    case "Created":
      return "Order Placed";
    case "Accepted":
      return "Order Confirmed";
    case "Packed":
      return "Order Packed";
    case "Delivered":
      return "Delivered";
    case "Cancelled":
      return "Cancelled";
    default:
      return status || "Processing";
  }
};

const OrderCard: FC<OrderCardProps> = ({ orderDetails, onPress }) => {
  const navigate = useNavigate();
  const items = orderDetails.farmers.flatMap((farmer) => farmer.items);
  const statusStyle = getStatusStyle(orderDetails.status);
  const StatusIcon = getStatusIcon(orderDetails.status);

  const getDeliveryTimeDisplay = () => {
    const time = orderDetails.deliveryTime
      ? dayjs(`${orderDetails.deliveryDate} ${orderDetails.deliveryTime}`)
          .format("ddd, D MMM, hh:mm A")
          .toUpperCase()
      : IsValidTime(orderDetails.estDeliveryTime)
      ? dayjs(`${orderDetails.deliveryDate} ${orderDetails.estDeliveryTime}`)
          .format("ddd, D MMM, hh:mm A")
          .toUpperCase()
      : `Delivery ${
          orderDetails.estDeliveryTime?.includes("min") ||
          orderDetails.estDeliveryTime?.includes("hours")
            ? "in"
            : "at"
        } ${orderDetails.estDeliveryTime}`;

    if (orderDetails.status?.toLowerCase() === "delivered") {
      return `Delivered on, ${time}`;
    }
    if (orderDetails.status?.toLowerCase() === "cancelled") {
      return `Cancelled `;
    }
    return time;
  };

  const { networkConfig, appDomain } = useAppConfigStore();

  const showPayNow =
    orderDetails?.balanceTobePaid > 0 && orderDetails.displayPrices !== false;

  const showPayNowForSeller = () => {
    if (orderDetails.sellerId === 56 && (orderDetails?.status === "Created" || orderDetails?.status === "Accepted" || orderDetails?.status === "Assigned" )) {
      return false;
    }
    return true
  }

  return (
    <div
      role="button"
      tabIndex={0}
      className="bg-white rounded-lg shadow-sm mb-4 p-4 cursor-pointer"
      onClick={onPress}
      onKeyDown={(e) => {
        if (e.key === "Enter" || e.key === " ") {
          onPress?.();
        }
      }}
    >
      {/* Store Info */}
      <div className="flex items-start mb-4">
        <div className="w-12 h-12 rounded-lg bg-gray-100 mr-3 flex-shrink-0 overflow-hidden shadow-sm flex justify-center items-center">
          <img
            src={networkConfig?.businessLogo || "/mandi_active.png"}
            alt={orderDetails.sellerName}
            className="w-full h-full object-cover"
          />
        </div>
        <div className="flex-1">
          <h3 className="text-md text-gray-900">{orderDetails.sellerName}</h3>
          <p className="text-xs text-blue-600">{getDeliveryTimeDisplay()}</p>
        </div>
      </div>

      {/* Dashed line above */}
      <div className="border-t border-dashed border-gray-200"></div>

      {/* Order Items */}
      {appDomain === "RET11" ? (
        <div className="flex flex-col gap-2 overflow-x-auto py-2">
          {items?.map((item: OrderItem, index: number) => {
            return (
              <div
                key={item.orderId + index}
                className="flex gap-2 items-start justify-between"
              >
                <div className="flex gap-2 items-center">
                  <DietaryImage dietary={item.diet} />
                  <p className="text-xs text-primary  border border-primary rounded-md w-6 h-5 bg-primary-50 flex items-center justify-center ml-2">
                    {item.qty}
                  </p>
                  <p className="mx-1 text-sm font-light">{"x"}</p>
                  <p className="text-xs text-gray-800 text-wrap">
                    {item.itemName}
                  </p>
                </div>
                {/* <p className="text-sm text-gray-800">
                  {formatCurrency(item.price * item.qty)}
                </p> */}
              </div>
            );
          })}
        </div>
      ) : (
        <div className="flex gap-2 overflow-x-auto py-2">
          {items?.slice(0, 5).map((item: OrderItem, index: number) => (
            <div key={item.orderId} className="flex-shrink-0 relative">
              <CustomImage
                src={item.itemUrl}
                alt={""}
                className="w-14 h-14 rounded-lg object-cover bg-gray-50 border border-neutral-100"
              />
              {index >= 4 && (
                <div className="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center">
                  <span className="text-white text-sm font-medium">
                    +{items.length - 4}
                  </span>
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Dashed line above */}
      <div className="border-t border-dashed border-gray-200 mb-2"></div>

      {/* Order Info */}
      <div className="flex flex-col gap-2">
        <div className="flex justify-between items-center">
          <div className="flex flex-col justify-between items-start gap-2">
            <span className="text-xs text-gray-500">
              Order placed on{" "}
              {dayjs(orderDetails.createdOn).format("DD MMM, h:mm A")}
            </span>
            <div className="flex items-center">
              <span
                className={`text-xs font-semibold italics flex gap-1 items-center ${
                  orderDetails.status === "Delivered" ||
                  orderDetails.status === "Cancelled"
                    ? "py-1"
                    : "px-2 py-1"
                } rounded-md ${statusStyle}`}
              >
                {StatusIcon}
                {getDisplayStatus(orderDetails.status)}
              </span>
            </div>
          </div>
          <div
            className="flex items-center cursor-pointer hover:text-teal-600 transition-colors"
            // onClick={handleTotalAmountClick}
            // role="button"
            // tabIndex={0}
            // onKeyDown={(e) => {
            //   if (e.key === "Enter" || e.key === " ") {
            //     handleTotalAmountClick(e as any);
            //   }
            // }}
          >
            {orderDetails.displayPrices !== false && (
              <span className="text-typography-600 text-sm font-semibold">
                {formatCurrency(orderDetails.totalAmount)}
              </span>
            )}
            <ChevronRight size={18} className="text-neutral-700" />
          </div>
        </div>

        {showPayNow && (
          <>
            {/* Dashed line above */}
            <div className="border-t border-dashed border-gray-200 mb-2"></div>

            <div className="flex justify-between items-center">
              <div className="flex flex-col gap-1">
                <span className="text-xs text-gray-700">
                  {orderDetails.status === "Delivered" ||
                  orderDetails.status === "Cancelled"
                    ? "Balance to be paid"
                    : "Amount to be paid"}
                </span>
                <span className="text-xs font-semibold">
                  {formatCurrency(orderDetails.balanceTobePaid)}
                </span>
              </div>
              <div className="flex items-center gap-3">
                {showPayNow && showPayNowForSeller() ? (
                  <PayNow
                    paymentRequest={orderToPaymentRequest(orderDetails)}
                    onSuccess={() => {
                      // Optionally handle success (e.g., refresh order list)
                      navigate(0);
                    }}
                  />
                ) : null}
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default OrderCard;
