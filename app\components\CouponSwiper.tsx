import React from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/autoplay";
import "swiper/css/pagination";
import { Autoplay, Pagination } from "swiper/modules";
import type { SwiperProps } from "swiper/react";
import type { AutoplayOptions, PaginationOptions } from "swiper/types";
import { cn } from "~/utils/cn";
import { SellerCouponDto } from "~/types";
import { formatCurrency } from "~/utils/format";
import { Truck } from "lucide-react";

interface CouponSwiperProps {
  coupons: SellerCouponDto[];
  autoplay?: boolean;
  pagination?: boolean;
  className?: string;
  swiperConfig?: Partial<SwiperProps>;
  onCouponClick?: (coupon: SellerCouponDto) => void;
}

const defaultSwiperConfig: Partial<SwiperProps> = {
  centeredSlides: true,
  spaceBetween: 10,
  slidesPerView: 1,
  loop: true,
  breakpoints: {
    640: {
      slidesPerView: 1.8
    },
    768: {
      slidesPerView: 2.2
    },
    1024: {
      slidesPerView: 2.8
    }
  },
  autoplay: {
    delay: 2000,
    disableOnInteraction: false
  }
};

const CouponCard: React.FC<{
  coupon: SellerCouponDto;
  onClick?: () => void;
}> = ({ coupon, onClick }) => {
  console.log(coupon);

  const getDiscountText = () => {
    if (coupon.discountType === "PERCENTAGE") {
      return `Get ${coupon.discountValue}% OFF`;
    } else if (coupon.discountType === "FIXED_AMOUNT") {
      return `Get Flat ${formatCurrency(coupon.discountValue)} OFF`;
    } else if (coupon.discountType === "FREE_DELIVERY") {
      return "Free Delivery";
    } else if (coupon.discountType === "FREE_ITEM") {
      return "Free Item";
    } else if (coupon.discountType === "HYBRID") {
      return coupon.description;
    }
  };

  const getConditionText = () => {
    if (coupon.discountType === "FREE_DELIVERY") {
      return `No coupon required ${
        coupon.minOrderValue > 0
          ? `| Above ${formatCurrency(coupon.minOrderValue)}`
          : " | No minimum order value"
      }`;
    } else if (coupon.discountType === "FREE_ITEM") {
      return "No coupon required | Free Item";
    } else if (coupon.discountType === "HYBRID") {
      return "No coupon required | Free Delivery and Free Item";
    } else {
      const minOrderText =
        coupon.minOrderValue > 0
          ? ` | Above ${formatCurrency(coupon.minOrderValue)}`
          : "";

      return minOrderText
        ? `Use coupon on cart${minOrderText}`
        : "Use coupon on cart | No minimum order value";
    }
  };

  const getIcon = () => {
    switch (coupon.discountType) {
      case "FREE_DELIVERY":
        return <img src="/delivery-2.svg" alt="delivery" className="w-6 h-6" />;
      default:
        return (
          <img
            src="/coupon-icon.svg"
            alt="coupon"
            className="w-6 h-6 rounded-full"
          />
        );
    }
  };

  return (
    <div
      className="flex flex-col gap-2 border border-[#2AD7DB] bg-gradient-to-r from-white to-[#dff9f8] p-3 rounded-lg shadow-md cursor-pointer transition-all duration-200 hover:shadow-lg"
      onClick={onClick}
      role="button"
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === "Enter" || e.key === " ") {
          onClick?.();
        }
      }}
    >
      <div className="flex gap-3 items-center">
        {/* Icon Container */}
        <div className="flex items-center justify-center bg-[#dff9f8] p-2 rounded-lg">
          {getIcon()}
        </div>

        {/* Text Content */}
        <div className="flex flex-col gap-1 flex-1">
          {/* Main Offer Text */}
          <h3 className="font-bold text-gray-800 text-sm leading-tight">
            {getDiscountText()}
          </h3>
          {/* Condition Text */}
          <p className="text-xs text-gray-600 leading-tight">
            {getConditionText()}
          </p>
        </div>
      </div>
    </div>
  );
};

const CouponSwiper: React.FC<CouponSwiperProps> = ({
  autoplay = true,
  pagination = false,
  coupons = [],
  className,
  swiperConfig = {},
  onCouponClick
}) => {
  const modules = [];

  if (autoplay) {
    modules.push(Autoplay);
  }

  if (pagination) {
    modules.push(Pagination);
  }

  if (coupons.length === 0) {
    return null;
  }

  if (coupons.length === 1) {
    return (
      <div className={cn("", className)}>
        <CouponCard
          coupon={coupons[0]}
          onClick={() => onCouponClick?.(coupons[0])}
        />
      </div>
    );
  }

  const finalSwiperConfig: Partial<SwiperProps> = {
    ...defaultSwiperConfig,
    ...swiperConfig,
    modules,
    pagination: pagination
      ? {
          clickable: true,
          ...((swiperConfig.pagination as Partial<PaginationOptions>) || {})
        }
      : false,
    autoplay: autoplay
      ? {
          ...(defaultSwiperConfig.autoplay as AutoplayOptions),
          ...((swiperConfig.autoplay as Partial<AutoplayOptions>) || {})
        }
      : false
  };

  return (
    <div className={cn("", className)}>
      <Swiper {...finalSwiperConfig} className="couponSwiper">
        {coupons.map((coupon, index) => (
          <SwiperSlide key={`${coupon.id}-${index}`}>
            <CouponCard
              coupon={coupon}
              onClick={() => onCouponClick?.(coupon)}
            />
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  );
};

export default CouponSwiper;
