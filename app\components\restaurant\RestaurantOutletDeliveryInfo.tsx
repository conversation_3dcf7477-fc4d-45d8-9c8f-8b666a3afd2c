import { Button } from "@headlessui/react";
import React from "react";
import TruncatedText from "../TruncatedText";
import { useNavigate } from "@remix-run/react";
import { AddressDto } from "~/types/address.types";
import { chooseitemsStore } from "~/stores/chooseitems.store";
import {
  ChevronDown,
  ChevronRight,
  MapPin,
  Store,
  UserRound
} from "lucide-react";
import { useAppConfigStore } from "~/stores/appConfig.store";
import { SellerInfo } from "~/types";

interface RestaurantOutletDeliveryInfoProps {
  sellerList: SellerInfo[];
  defaultAddress: AddressDto;
  onSellerClick: () => void;
  onAddressClick?: () => void;
  onProfileClick?: () => void;
}

const RestaurantOutletDeliveryInfo: React.FC<RestaurantOutletDeliveryInfoProps> = ({
  sellerList,
  defaultAddress,
  onSellerClick,
  onAddressClick,
  onProfileClick
}) => {
  const navigate = useNavigate();
  const { itemOptionsData } = chooseitemsStore((state) => state);
  const { appDomain } = useAppConfigStore((state) => state);

  // Find current seller from the list
  const currentSeller = sellerList.find(seller => seller.id === itemOptionsData?.sellerId);
  const currentSellerName = currentSeller?.name || itemOptionsData?.sellerName || "Select Outlet";

  const handleAddressClick = () => {
    if (onAddressClick) {
      onAddressClick();
    } else {
      if (appDomain === "RET11") {
        navigate(`/changeaddress?redirectTo=/home/<USER>/rsrp`, {
          state: {
            address: defaultAddress,
            isEdit: true
          }
        });
      } else {
        navigate(`/changeaddress?redirectTo=/chooseitems`, {
          state: {
            address: defaultAddress,
            isEdit: true
          }
        });
      }
    }
  };

  return (
    <>
      <div className="flex flex-col justify-between items-center px-3 pt-4">
        <div className="flex flex-col justify-between w-full">
          {/* Outlet Selection Section */}
          <div className="flex flex-row items-center justify-between relative">
            <Button
              className="flex flex-row items-center gap-2 mb-1 w-full"
              onClick={onSellerClick}
            >
              <div>
                <Store size={24} className="text-white" />
              </div>
              <div className="flex flex-col items-start">
                <div className="flex items-center gap-1 text-sm font-semibold text-white">
                  <TruncatedText
                    text={currentSellerName}
                    className="max-w-[12rem] text-white"
                  />
                  <ChevronDown className="w-4 h-4 text-white" />
                </div>
                <TruncatedText
                  className="text-[.7rem] font-light max-w-[16rem]"
                  text={
                    currentSeller?.address ||
                    "Tap to change outlet"
                  }
                />
              </div>
            </Button>

            <Button
              onClick={onProfileClick}
              className="rounded-full bg-black bg-opacity-30 p-2"
            >
              <UserRound size={20} fill="#FFF" strokeWidth={0} />
            </Button>
          </div>

          <div className="h-3 border-l-[3px] border-dotted border-white opacity-80 ml-2.5" />

          {/* Address Selection Section */}
          <Button
            className="text-sm flex items-center gap-2"
            onClick={handleAddressClick}
          >
            <div className="">
              <MapPin size={24} />
            </div>
            <div className="flex flex-col items-start">
              <div className="flex items-center gap-1 text-sm font-semibold">
                {(defaultAddress?.address &&
                  defaultAddress.name?.toLocaleUpperCase()) ||
                  "Add your delivery address"}
                <ChevronDown className="w-4 h-4" />
              </div>
              <TruncatedText
                className="text-[.7rem] font-light max-w-[16rem]"
                text={
                  defaultAddress.address ||
                  "Check availability and menu items at your location"
                }
              />
            </div>
          </Button>
        </div>
      </div>
    </>
  );
};

export default RestaurantOutletDeliveryInfo;
